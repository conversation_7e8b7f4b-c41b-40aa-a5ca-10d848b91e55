<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="调拨申请编号">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入调拨申请编号" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="调出仓库">
								<el-select placeholder="请选择调出仓库" clearable v-model="state.queryForm.warehouseId">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="调拨状态">
								<el-select placeholder="请选择调拨状态" clearable v-model="state.queryForm.billStatus">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待审批', value: 0 },
											{ label: '待出库', value: 1 },
											{ label: '驳回', value: 2 },
											{ label: '待接收', value: 3 },
											{ label: '完成', value: 4 },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="申请时间">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="routerClick()">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="调拨申请单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="调出仓库" prop="outWarehouse" show-overflow-tooltip></el-table-column>
				<el-table-column label="接收仓库" prop="entryWarehouse" show-overflow-tooltip></el-table-column>
				<el-table-column label="调拨状态"  show-overflow-tooltip>
					<template #default="scope">
					<span v-if="scope.row.billStatus == 0">待审批</span>
					<span v-if="scope.row.billStatus == 1">待出库</span>
					<span v-if="scope.row.billStatus == 2">驳回</span>
					<span v-if="scope.row.billStatus == 3">待接收</span>
					<span v-if="scope.row.billStatus == 4">完成</span>
					</template>
				</el-table-column>
				<el-table-column label="审批部门" prop="approveDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="审批人员" prop="approveUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请时间" prop="applyTime" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改  add-->
						<el-button icon="edit-pen" text type="primary" @click="routerClick(scope.row.id)" v-if="scope.row.billStatus == 0">
							{{ $t('common.editBtn') }}
						</el-button>
						<!-- 删除 -->
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary" v-if="scope.row.billStatus == 0">{{ $t('common.delBtn') }} </el-button>
						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj, getWarehouse } from '/@/api/storage/allocate/applyFor';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

const param_type = ref([]);
// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		warehouseId: '',
		billStatus: '',
		time: '',
		beginApplyTime: '',
		endApplyTime: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/applyFor/form',
		query: { id: id, notCreateTags: 'true' },
	});
};

//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? `修改:${id}` : '新增';
	router.push({
		path: '/storage/allocate/applyFor/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};

const dateChange = (value: any) => {
	state.queryForm.beginApplyTime = '';
	state.queryForm.endApplyTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginApplyTime = value[0];
	state.queryForm.endApplyTime = value[1];
};
// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};

onMounted(() => {
	getWarehouseData();
});
</script>
