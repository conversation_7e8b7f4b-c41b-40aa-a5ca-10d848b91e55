<!-- 过期-->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full">
					<div class="float-left">
						<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
							<el-form-item label="物资名称" prop="logType">
								<el-input clearable placeholder="请输入物资名称" v-model="state.queryForm.materialName"></el-input>
							</el-form-item>
							<el-form-item label="有效期" prop="time">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" icon="Search" type="primary">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<!-- <div class="float-right">
						<el-button @click="getDataList"  type="primary">导出</el-button>
					</div> -->
				</div>
			</el-row>

			<el-table
				ref="tableRef"
				:data="state.dataList"
				@sort-change="sortChangeHandle"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="条码" prop="materialBar" show-overflow-tooltip></el-table-column>

				<el-table-column label="存放位置" prop="locationBar" show-overflow-tooltip></el-table-column>
				<el-table-column label="有效期至" prop="endEffectiveTime" show-overflow-tooltip></el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { expiredPageList } from '/@/api/storage/earlyWarning';
import { useI18n } from 'vue-i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

const { log_type } = useDict('log_type');

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

let tableRef = ref(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		materialName: '',
		time: '',
		beginEndEffectiveTime: '',
		overEndEffectiveTime: '',
	},
	pageList: expiredPageList,
});

//  table hook
const { downBlobFile, getDataList, currentChangeHandle: baseCurrentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 分页事件
const currentChangeHandle = (page: number) => {
	// Reset table scroll position to top
	tableRef.value?.setScrollTop(0);
	// Call the original handler
	baseCurrentChangeHandle(page);
};

const dateChange = (value: any) => {
	state.queryForm.beginEndEffectiveTime = '';
	state.queryForm.overEndEffectiveTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginEndEffectiveTime = value[0];
	state.queryForm.overEndEffectiveTime = value[1];
};

// onMounted 通过路由参数给  serviceId 赋值
const route = useRoute();
onMounted(() => {
	const { serviceId } = route.query;
	if (serviceId) {
		state.queryForm.serviceId = serviceId;
	}
	getDataList();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
