<template>
	<div>
		<el-row>
			<div class="mt8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item label="所属仓库" prop="warehouse">
							<el-select v-model="state.queryForm.warehouseId" placeholder="请选择所属仓库" clearable class="w-80">
								<el-option v-for="item in warehouseData" :key="item.index" :label="item.warehouseName" :value="item.id" />
							</el-select>
						</el-form-item>
						<el-form-item label="mac" prop="mac">
							<el-input placeholder="请输入mac" v-model="state.queryForm.mac" style="max-width: 180px" clearable />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
						</el-form-item>
					</el-form>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			max-height="620px"

			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
		>
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="mac" label="巷道灯mac" show-overflow-tooltip />
			<el-table-column prop="manufacture" label="型号" show-overflow-tooltip />
			<el-table-column prop="warehouseName" label="所属仓库" show-overflow-tooltip> </el-table-column>
			<el-table-column prop="routerIp" label="所属基站IP" show-overflow-tooltip />
			<el-table-column prop="routerIdentity" label="所属基站ID" show-overflow-tooltip />
			<el-table-column prop="rssi" label="信号强度" show-overflow-tooltip />
			<el-table-column label="在线状态" show-overflow-tooltip>
				<template #default="scope">
					{{ scope.row.state ? '是' : '否' }}
				</template>
			</el-table-column>
			<el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
	</div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { pageList, getWarehouse } from '/@/api/basicData/selectionManagement/lamp';

// 搜索变量
const queryRef = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: void 0,
		mac: '',
	},
	pageList,
});
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 获取仓库数据
let warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};

onMounted(() => {
	getWarehouseData();
});
</script>
