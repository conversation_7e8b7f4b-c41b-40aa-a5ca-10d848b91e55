<template>
	<el-dialog :title="form.id ? '绑定' : '新增'" v-model="visible" :close-on-click-modal="false" draggable>
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px">
			<el-row :gutter="20">
				<el-col :span="12" class="mb20">
					<el-form-item label="仓库" prop="warehouseId">
						<el-select v-model="form.warehouseId" placeholder="请选择仓库" @change="getZoneCodeSelect" :disabled="form.id !== ''">
							<el-option v-for="item in warehouseData" :key="item.id" :label="item.warehouseName" :value="item.id" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="区号" prop="warehouseZoneId">
						<el-select v-model="form.warehouseZoneId" placeholder="请选择区号" @change="getColumnSelect" :disabled="form.id !== ''">
							<el-option v-for="item in zoneCodeData" :key="item.zoneCode" :label="item.zoneCode" :value="item.id" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="列号" prop="shelfCode">
						<el-select v-model="form.shelfCode" placeholder="请选择列号" @change="columnChange" :disabled="form.id !== ''">
							<el-option v-for="item in columnCodeData" :key="item.id" :label="item.shelfCode" :value="item.shelfCode" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20" v-if="faceStatus == '1'">
					<el-form-item label="面" prop="faceCode">
						<el-select v-model="form.faceCode" placeholder="请选择面" @change="faceChange" :disabled="form.id !== ''">
							<el-option
								v-for="item in [
									{ label: '左面', value: '01' },
									{ label: '右面', value: '02' },
								]"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20" v-if="groupNum">
					<el-form-item label="组号" prop="groupCode">
						<el-select v-model="form.groupCode" placeholder="请选择组号" @change="groupChange" :disabled="form.id !== ''">
							<el-option v-for="item in groupData" :key="item.value" :label="item.groupCode" :value="item.groupCode" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20" v-if="levelNum">
					<el-form-item label="层号" prop="levelCode">
						<el-select v-model="form.levelCode" placeholder="请选择层号" @change="levelChange" :disabled="form.id !== ''">
							<el-option v-for="item in levelData" :key="item.id" :label="item.levelCode" :value="item.levelCode" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="货位号" prop="cellCode">
						<el-input v-model="form.cellCode" placeholder="请输入货位号" @change="cellChange" :disabled="form.id !== ''" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="位置编码" prop="locationCode">
						<el-input v-model="form.locationCode" placeholder="自动生成" disabled />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="位置条码" prop="locationBar">
						<el-input v-model="form.locationBar" placeholder="自动生成" disabled />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="标签mac" prop="mac">
						<el-input v-model="form.mac" placeholder="请输入标签mac" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="SysMessageSmsDialog">
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, bindObj, getWarehouse, getArea, getColumn, getGroup, getLayer } from '/@/api/basicData/locationManagement/label';
import { rule } from '/@/utils/validate';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive<any>({
	id: '',
	warehouseId: '',
	warehouseZoneId: '',
	shelfCode: '',
	faceCode: '',
	groupCode: '',
	levelCode: '',
	cellCode: '',
	locationCode: '',
	locationBar: '',
	mac: '',
});

// 定义校验规则
const dataRules = ref({
	warehouseId: [{ required: true, message: '仓库不能为空', trigger: 'change' }],
	warehouseZoneId: [{ required: true, message: '区号不能为空', trigger: 'change' }],
	shelfCode: [{ required: true, message: '列号不能为空', trigger: 'change' }],
	faceCode: [{ required: true, message: '面不能为空', trigger: 'change' }],
	groupCode: [{ required: true, message: '组号不能为空', trigger: 'change' }],
	levelCode: [{ required: true, message: '层号不能为空', trigger: 'change' }],
	cellCode: [
		{ required: true, validator: rule.fourNumber, trigger: 'change' },
		{ required: true, message: '货位号不能为空', trigger: 'change' },
	],
	mac: [{ required: true, message: '标签mac不能为空', trigger: 'blur' }],
});
let positionEncoding = ref(new Array(7));
let locationBarcode = ref(new Array(7));
// 打开弹窗
const openDialog = (id: string) => {
	resetData(); // 调用重置函数

	visible.value = true;

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 获取sysMessage信息
	if (id) {
		form.id = id;
		getObj(id).then((res: any) => {
			res.data.warehouseId ? getZoneCodeSelect(res.data.warehouseId) : '';
			res.data.warehouseZoneId ? getColumnSelect(res.data.warehouseZoneId) : '';
			res.data.shelfCode ? columnChange(res.data.shelfCode) : '';
			res.data.faceCode ? faceChange(res.data.faceCode) : '';
			res.data.groupCode ? groupChange(res.data.groupCode) : '';
			res.data.levelCode ? levelChange(res.data.levelCode) : '';
			res.data.cellCode ? cellChange(res.data.cellCode) : '';
			Object.assign(form, res.data);
		});
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		let { id, ...other } = form;
		form.id ? await bindObj({ mac: form.mac, locationCode: form.locationCode }) : await addObj({ ...other, zoneCode: locationBarcode.value[1] });

		useMessage().success(form.id ? '绑定成功' : t('common.addSuccessText'));

		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

let faceStatus = ref('0');
let groupNum = ref('');
let levelNum = ref('');

let warehouseData = ref<any>([]);
let zoneCodeData = ref<any>([]);
let columnCodeData = ref<any>([]);
let groupData = ref<any>([]);
let levelData = ref<any>([]);

//仓库下拉
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
// 更新位置编码和位置条码的通用函数
const updatePositionAndBarcode = (index: number, prefix: string, value: string) => {
	positionEncoding.value[index] = prefix + value;
	locationBarcode.value[index] = value;
};

// 仓库获取区号下拉
const getZoneCodeSelect = async (id: string) => {
	locationBarcode.value[1] = '';
	locationBarcode.value[2] = '';
	locationBarcode.value[3] = '';
	locationBarcode.value[4] = '';
	locationBarcode.value[5] = '';

	positionEncoding.value[1] = '';
	positionEncoding.value[2] = '';
	positionEncoding.value[3] = '';
	positionEncoding.value[4] = '';
	positionEncoding.value[5] = '';
	resetForm(['warehouseZoneId', 'shelfCode', 'groupCode', 'levelCode', 'faceCode']);
	zoneCodeData.value = (await getArea(id))?.data || [];
	const warehouseCode = warehouseData.value.find((item: any) => item.id === id)?.warehouseCode;
	updatePositionAndBarcode(0, 'W', warehouseCode || '');
	generate();
};

// 区获取列号下拉
const getColumnSelect = async (id: string) => {
	locationBarcode.value[2] = '';
	locationBarcode.value[3] = '';
	locationBarcode.value[4] = '';
	locationBarcode.value[5] = '';

	positionEncoding.value[2] = '';
	positionEncoding.value[3] = '';
	positionEncoding.value[4] = '';
	positionEncoding.value[5] = '';
	resetForm(['shelfCode', 'groupCode', 'levelCode', 'faceCode']);

	columnCodeData.value = (await getColumn(id))?.data || [];
	const zoneCode = zoneCodeData.value.find((item: any) => item.id === id)?.zoneCode;
	faceStatus.value = zoneCodeData.value.find((item: any) => item.id === id)?.faceStatus;
	groupNum.value = zoneCodeData.value.find((item: any) => item.id === id)?.groupNum;
	levelNum.value = zoneCodeData.value.find((item: any) => item.id === id)?.levelNum;
	updatePositionAndBarcode(1, 'Z', zoneCode || '');
	if (!faceStatus.value) {
		locationBarcode.value[3] = '00';
	}
	if (!groupNum.value) {
		locationBarcode.value[4] = '00';
	}
	if (!levelNum.value) {
		locationBarcode.value[5] = '00';
	}
	generate();
};

// 列号触发
const columnChange = async (value: string) => {
	resetForm(['groupCode', 'levelCode']);
	updatePositionAndBarcode(2, 'S', value);

	let params: any = {
		warehouseZoneId: form.warehouseZoneId,
		shelfCode: locationBarcode.value[2],
	};

	if (faceStatus.value && form.faceCode) {
		params.faceCode = locationBarcode.value[3];
	}

	groupData.value = (await getGroup(params))?.data || [];
	console.log("🚀 ~ columnChange ~ groupData:", groupData)

	let params1: any = { ...params };

	if (groupNum.value && form.groupCode) {
		params1.groupCode = locationBarcode.value[4];
	}

	levelData.value = (await getLayer(params1))?.data || [];
	console.log("🚀 ~ groupChange ~ levelData.value :",levelData.value)
	generate();
};

// 面获取 组下拉
const faceChange = async (value: string) => {
	resetForm(['groupCode', 'levelCode']);
	updatePositionAndBarcode(3, 'F', value);
	let params: any = {
		warehouseZoneId: form.warehouseZoneId,
		shelfCode: locationBarcode.value[2],
	};

	if (faceStatus.value && form.faceCode) {

		params.faceCode = locationBarcode.value[3];
	}
	groupData.value = (await getGroup(params))?.data || [];
	console.log("🚀 ~ faceChange 面面面~ groupData:", groupData)
	generate();
};

// 组号获取层下拉
const groupChange = async (id: string) => {
	resetForm(['levelCode']);
	updatePositionAndBarcode(4, 'G', id);
	let params: any = {
		warehouseZoneId: form.warehouseZoneId,
		shelfCode: locationBarcode.value[2],
	};

	if (faceStatus.value && form.faceCode) {
		params.faceCode = locationBarcode.value[3];
	}
	if (groupNum.value && form.groupCode) {
		params.groupCode = locationBarcode.value[4];
	}
	let res= (await getLayer(params))?.data || [];
	levelData.value =[...res]
	console.log("🚀 ~ groupChange 组组~ levelData:", levelData.value)

	nextTick(() => {
    generate();
  });
};

// 层号触发
const levelChange = (value: string) => {
	updatePositionAndBarcode(5, 'L', value);
	generate();
};

// 货位号触发
const cellChange = (id: string) => {
	updatePositionAndBarcode(6, 'C', id);
	generate();
};

// 计算位置编码 位置条码
const generate = () => {
	form.locationCode = positionEncoding.value.join('');
	form.locationBar = locationBarcode.value.join('');
};
//重置表单方法
const resetForm = (arr: any[]) => {
	arr.forEach((item) => {
		form[item] = '';
	});
};

// 新增数据重置函数
const resetData = () => {
	faceStatus.value = '0';
	groupNum.value = '';
	levelNum.value = '';
	zoneCodeData.value = [];
	columnCodeData.value = [];
	groupData.value = [];
	levelData.value = [];
	positionEncoding.value = new Array(7);
	locationBarcode.value = new Array(7);
	form.id = '';
	form.warehouseId = '';
	form.warehouseZoneId = '';
	form.shelfCode = '';
	form.faceCode = '';
	form.groupCode = '';
	form.levelCode = '';
	form.cellCode = '';
	form.locationCode = '';
	form.locationBar = '';
	form.mac = '';
};
onMounted(() => {
	getWarehouseSelect();
});
// 暴露变量
defineExpose({
	openDialog,
});
</script>
