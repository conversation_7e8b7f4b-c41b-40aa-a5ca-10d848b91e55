<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="调拨申请单编号">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入调拨单申请编号" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="接收仓库">
								<el-select placeholder="请选择接收仓库" clearable v-model="state.queryForm.warehouseId">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="调拨状态">
								<el-select placeholder="请选择调拨状态" clearable v-model="state.queryForm.billStatus">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待审批', value: 0 },
											{ label: '待出库', value: 1 },
											{ label: '驳回', value: 2 },
											{ label: '待接收', value: 3 },
											{ label: '完成', value: 4 },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="申请时间">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="调拨申请单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="调出仓库" prop="outWarehouse" show-overflow-tooltip></el-table-column>
				<el-table-column label="接收仓库" prop="entryWarehouse" show-overflow-tooltip></el-table-column>
				<el-table-column label="调拨状态" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.billStatus == 0">待审批</span>
						<span v-if="scope.row.billStatus == 1">待出库</span>
						<span v-if="scope.row.billStatus == 2">驳回</span>
						<span v-if="scope.row.billStatus == 3">待接收</span>
						<span v-if="scope.row.billStatus == 4">完成</span>
					</template>
				</el-table-column>
				<el-table-column label="申请部门" prop="applyDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请人员" prop="applyUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请时间" prop="applyTime" show-overflow-tooltip></el-table-column>

				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 审批approve-->
						<el-button icon="" text type="primary" @click="confirmClick(scope.row.id)" v-if="scope.row.billStatus == 0"> 审批 </el-button>
						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList, getWarehouse } from '/@/api/storage/allocate/appRove';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

const router = useRouter();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		warehouseId: '',
		billStatus: '',
		time: '',
		beginApplyTime: '',
		endApplyTime: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

//查看
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/Approve/form',
		query: { id: id, notCreateTags: 'true' },
	});
};

//审批
const confirmClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/Approve/approve',
		query: { id: id, notCreateTags: 'true' },
	});
};

const dateChange = (value: any) => {
	state.queryForm.beginApplyTime = '';
	state.queryForm.endApplyTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginApplyTime = value[0];
	state.queryForm.endApplyTime = value[1];
};
// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};

onMounted(() => {
	getWarehouseData();
});
</script>
