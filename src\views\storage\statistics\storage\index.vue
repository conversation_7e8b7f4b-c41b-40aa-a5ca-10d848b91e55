<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="仓库" prop="logType">
								<el-select placeholder="请选择仓库" clearable v-model="state.queryForm.warehouseId" class="!w-[160px]">
									<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="入库类别" prop="logType">
								<el-select placeholder="请选择入库类别" clearable v-model="state.queryForm.bilType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in bill_type" />
								</el-select>
							</el-form-item>
							<el-form-item label="时间区间" prop="time">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="exportClick"> 导出 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库类别" prop="billType" show-overflow-tooltip></el-table-column>
				<el-table-column label="单据数量" prop="count" show-overflow-tooltip></el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { sPageList, getWarehouse} from '/@/api/storage/statistics';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { bill_type } = useDict('bill_type');
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		bilType: '',
		beginEndEffectiveTime: '',
		overEndEffectiveTime: '',
		time: '',
	},
	pageList: sPageList,
	createdIsNeed: false,

});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);
const dateChange = (value: any) => {
	state.queryForm.beginEndEffectiveTime = ''
	state.queryForm.overEndEffectiveTime = ''
	if (!Array.isArray(value)) return;
	state.queryForm.beginEndEffectiveTime = value[0];
	state.queryForm.overEndEffectiveTime = value[1];
};

const exportClick = () => {
	downBlobFile('/admin/inboundStatistics/getExportInboundStatistics', state.queryForm, '入库单统计.xlsx');
};
let warehouseData = ref<any>([]);

const getWarehouseData = async () => {
	const res = await getWarehouse();
	warehouseData.value = res.data;
};

onMounted(() => {
	getWarehouseData();
});
</script>
